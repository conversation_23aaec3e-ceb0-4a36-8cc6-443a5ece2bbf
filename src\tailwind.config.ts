import type { Config } from "tailwindcss";

const config: Config = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        // 酒仙湖品牌色彩体系
        "lake-green": {
          50: "#f0f7f4",
          100: "#dcebe3",
          200: "#bbd8ca",
          300: "#8fbfa8",
          400: "#5A8F7B", // 主色
          500: "#4a7c69",
          600: "#3c6355",
          700: "#325045",
          800: "#2a4038",
          900: "#24362f",
        },
        "ink-black": {
          50: "#f8f9fa",
          100: "#e9ecef",
          200: "#dee2e6",
          300: "#ced4da",
          400: "#adb5bd",
          500: "#6c757d",
          600: "#495057",
          700: "#343A40", // 主色
          800: "#212529",
          900: "#1a1e21",
        },
        "elegant-gold": {
          50: "#faf8f3",
          100: "#f4f0e6",
          200: "#e8dcc7",
          300: "#dbc4a0",
          400: "#C5A880", // 主色
          500: "#b8956b",
          600: "#a6825a",
          700: "#8a6b4c",
          800: "#715742",
          900: "#5c4837",
        },
        "rice-white": {
          50: "#ffffff",
          100: "#F8F9FA", // 主色
          200: "#f1f3f4",
          300: "#e8eaed",
          400: "#dadce0",
          500: "#bdc1c6",
          600: "#9aa0a6",
          700: "#5f6368",
          800: "#3c4043",
          900: "#202124",
        },
        "wine-red": {
          50: "#fdf2f2",
          100: "#fce8e8",
          200: "#fbd5d5",
          300: "#f8b4b4",
          400: "#f98080",
          500: "#f05252",
          600: "#e02424",
          700: "#9B2C2C", // 主色
          800: "#7f1d1d",
          900: "#771d1d",
        },
      },
      fontFamily: {
        chinese: ["PingFang SC", "Noto Sans CJK SC", "Microsoft YaHei", "sans-serif"],
        english: ["Roboto", "Open Sans", "sans-serif"],
        brush: ["STKaiti", "KaiTi", "楷体", "serif"],
        title: ["Noto Sans", "Source Han Sans", "sans-serif"],
      },
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1rem" }],
        sm: ["0.875rem", { lineHeight: "1.25rem" }],
        base: ["1rem", { lineHeight: "1.5rem" }],
        lg: ["1.125rem", { lineHeight: "1.75rem" }],
        xl: ["1.25rem", { lineHeight: "1.75rem" }],
        "2xl": ["1.5rem", { lineHeight: "2rem" }],
        "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
        "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
        "5xl": ["3rem", { lineHeight: "1" }],
        "6xl": ["3.75rem", { lineHeight: "1" }],
        "7xl": ["4.5rem", { lineHeight: "1" }],
        "8xl": ["6rem", { lineHeight: "1" }],
        "9xl": ["8rem", { lineHeight: "1" }],
      },
      spacing: {
        "18": "4.5rem",
        "88": "22rem",
        "128": "32rem",
        "144": "36rem",
      },
      borderRadius: {
        xl: "0.75rem",
        "2xl": "1rem",
        "3xl": "1.5rem",
      },
      boxShadow: {
        soft: "0 2px 8px rgba(90, 143, 123, 0.1)",
        medium: "0 4px 16px rgba(90, 143, 123, 0.15)",
        strong: "0 8px 32px rgba(90, 143, 123, 0.2)",
        glow: "0 0 20px rgba(90, 143, 123, 0.3)",
      },
      backgroundImage: {
        "gradient-lake": "linear-gradient(135deg, #5A8F7B 0%, #4A7C69 100%)",
        "gradient-sunset": "linear-gradient(135deg, #C5A880 0%, #B8956B 100%)",
        "gradient-ink": "linear-gradient(135deg, #343A40 0%, #212529 100%)",
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "ink-wash": "radial-gradient(circle at 30% 70%, rgba(90, 143, 123, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(197, 168, 128, 0.08) 0%, transparent 50%)",
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "fade-in-up": "fadeInUp 0.8s ease-out",
        "fade-in-down": "fadeInDown 0.8s ease-out",
        "fade-in-left": "fadeInLeft 0.8s ease-out",
        "fade-in-right": "fadeInRight 0.8s ease-out",
        "slide-up": "slideUp 0.5s ease-out",
        "slide-down": "slideDown 0.5s ease-out",
        float: "float 3s ease-in-out infinite",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "bounce-slow": "bounce 2s infinite",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeInUp: {
          "0%": { opacity: "0", transform: "translateY(30px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        fadeInDown: {
          "0%": { opacity: "0", transform: "translateY(-30px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
        fadeInLeft: {
          "0%": { opacity: "0", transform: "translateX(-30px)" },
          "100%": { opacity: "1", transform: "translateX(0)" },
        },
        fadeInRight: {
          "0%": { opacity: "0", transform: "translateX(30px)" },
          "100%": { opacity: "1", transform: "translateX(0)" },
        },
        slideUp: {
          "0%": { transform: "translateY(100%)" },
          "100%": { transform: "translateY(0)" },
        },
        slideDown: {
          "0%": { transform: "translateY(-100%)" },
          "100%": { transform: "translateY(0)" },
        },
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
      },
      screens: {
        xs: "375px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        "3xl": "1920px",
      },
      container: {
        center: true,
        padding: {
          DEFAULT: "1rem",
          sm: "1.5rem",
          lg: "2rem",
          xl: "2.5rem",
          "2xl": "3rem",
        },
        screens: {
          sm: "640px",
          md: "768px",
          lg: "1024px",
          xl: "1280px",
          "2xl": "1400px",
        },
      },
      aspectRatio: {
        "4/3": "4 / 3",
        "3/2": "3 / 2",
        "2/3": "2 / 3",
        "9/16": "9 / 16",
      },
      zIndex: {
        "60": "60",
        "70": "70",
        "80": "80",
        "90": "90",
        "100": "100",
      },
      backdropBlur: {
        xs: "2px",
      },
      transitionProperty: {
        height: "height",
        spacing: "margin, padding",
      },
      transitionDuration: {
        "400": "400ms",
        "600": "600ms",
      },
      transitionTimingFunction: {
        "bounce-in": "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
        smooth: "cubic-bezier(0.4, 0, 0.2, 1)",
      },
    },
  },
  plugins: [],
};

export default config;
