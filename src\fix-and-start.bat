@echo off
echo 🚀 酒仙湖项目启动脚本
echo.

echo 📦 清理缓存和依赖...
if exist node_modules rmdir /s /q node_modules
if exist yarn.lock del yarn.lock
if exist package-lock.json del package-lock.json

echo.
echo 📥 重新安装依赖...
call yarn install

echo.
echo 🔧 验证配置文件...
if not exist tailwind.config.ts (
    echo ❌ tailwind.config.ts 不存在
    exit /b 1
)

if not exist vite.config.ts (
    echo ❌ vite.config.ts 不存在
    exit /b 1
)

echo ✅ 配置文件检查完成

echo.
echo 🚀 启动开发服务器...
call yarn run dev

pause
