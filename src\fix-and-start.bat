@echo off
chcp 65001 >nul
echo Jiuxianhu Project Startup Script
echo.

echo Cleaning cache and dependencies...
if exist node_modules rmdir /s /q node_modules
if exist yarn.lock del yarn.lock
if exist package-lock.json del package-lock.json
if exist .vite rmdir /s /q .vite

echo.
echo Reinstalling dependencies...
call yarn install

echo.
echo Verifying config files...
if not exist tailwind.config.ts (
    echo ERROR: tailwind.config.ts not found
    exit /b 1
)

if not exist vite.config.js (
    echo ERROR: vite.config.js not found
    exit /b 1
)

if not exist postcss.config.js (
    echo ERROR: postcss.config.js not found
    exit /b 1
)

echo Config files check completed

echo.
echo Starting development server...
call yarn run dev

pause
