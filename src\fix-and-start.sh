#!/bin/bash

echo "🚀 酒仙湖项目启动脚本"
echo ""

echo "📦 清理缓存和依赖..."
rm -rf node_modules yarn.lock package-lock.json .vite

echo ""
echo "📥 重新安装依赖..."
yarn install

echo ""
echo "🔧 验证配置文件..."
if [ ! -f "tailwind.config.ts" ]; then
    echo "❌ tailwind.config.ts 不存在"
    exit 1
fi

if [ ! -f "vite.config.js" ]; then
    echo "❌ vite.config.js 不存在"
    exit 1
fi

if [ ! -f "postcss.config.js" ]; then
    echo "❌ postcss.config.js 不存在"
    exit 1
fi

echo "✅ 配置文件检查完成"

echo ""
echo "🚀 启动开发服务器..."
yarn run dev
