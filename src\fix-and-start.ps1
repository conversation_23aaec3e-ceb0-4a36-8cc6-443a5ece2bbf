# Jiuxianhu Project Startup Script for Windows
Write-Host "Jiuxianhu Project Startup Script" -ForegroundColor Green
Write-Host ""

Write-Host "Cleaning cache and dependencies..." -ForegroundColor Yellow
if (Test-Path "node_modules") { Remove-Item -Recurse -Force "node_modules" }
if (Test-Path "yarn.lock") { Remove-Item -Force "yarn.lock" }
if (Test-Path "package-lock.json") { Remove-Item -Force "package-lock.json" }
if (Test-Path ".vite") { Remove-Item -Recurse -Force ".vite" }

Write-Host ""
Write-Host "Reinstalling dependencies..." -ForegroundColor Yellow
yarn install

Write-Host ""
Write-Host "Verifying config files..." -ForegroundColor Yellow

if (-not (Test-Path "tailwind.config.ts")) {
    Write-Host "ERROR: tailwind.config.ts not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "vite.config.js")) {
    Write-Host "ERROR: vite.config.js not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "postcss.config.js")) {
    Write-Host "ERROR: postcss.config.js not found" -ForegroundColor Red
    exit 1
}

Write-Host "Config files check completed" -ForegroundColor Green

Write-Host ""
Write-Host "Starting development server..." -ForegroundColor Green
yarn run dev

Read-Host "Press Enter to exit"
