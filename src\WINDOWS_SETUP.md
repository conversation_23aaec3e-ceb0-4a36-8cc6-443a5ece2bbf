# Windows 11 快速启动指南

## 🚀 方法 1: 使用 PowerShell 脚本 (推荐)

```powershell
# 在 PowerShell 中运行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\fix-and-start.ps1
```

## 🔧 方法 2: 手动执行命令

在 PowerShell 中逐步执行以下命令：

### 步骤 1: 清理缓存
```powershell
Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
Remove-Item -Force yarn.lock -ErrorAction SilentlyContinue
Remove-Item -Force package-lock.json -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force .vite -ErrorAction SilentlyContinue
```

### 步骤 2: 安装依赖
```powershell
yarn install
```

### 步骤 3: 启动项目
```powershell
yarn run dev
```

## 🛠 方法 3: 使用 npm (如果 yarn 有问题)

```powershell
# 清理
Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
Remove-Item -Force yarn.lock -ErrorAction SilentlyContinue

# 安装和启动
npm install
npm run dev
```

## 🎯 预期结果

成功启动后，您应该看到：

```
  VITE v4.5.0  ready in 1234 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
```

浏览器会自动打开 http://localhost:3000 显示酒仙湖网站。

## 🔍 常见问题解决

### 问题 1: PowerShell 执行策略
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 问题 2: 端口被占用
```powershell
# 查找占用 3000 端口的进程
netstat -ano | findstr :3000

# 杀死进程 (替换 PID)
taskkill /PID <进程ID> /F
```

### 问题 3: yarn 命令不存在
```powershell
# 安装 yarn
npm install -g yarn

# 或者直接使用 npm
npm install
npm run dev
```

## 📋 文件检查清单

确保以下文件存在：
- ✅ `package.json`
- ✅ `vite.config.js`
- ✅ `tailwind.config.ts`
- ✅ `postcss.config.js`
- ✅ `tsconfig.json`
- ✅ `index.html`
- ✅ `main.tsx`

## 🎉 成功标志

项目启动成功后，您将看到：
- 🏞️ 酒仙湖首页加载完成
- 🎨 新中式设计风格展现
- 📱 响应式布局正常工作
- ⚡ 热重载功能可用

---

**技术支持**: 如有问题，请检查 Node.js 版本 (需要 >= 18.0.0)
